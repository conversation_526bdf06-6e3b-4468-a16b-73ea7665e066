﻿namespace Simulateur.Communication
{
    internal class WatchDog
    {
        private readonly int _delaySeconds;
        private readonly Action _sendMessageAction;
        private readonly CancellationToken _token;

        public WatchDog(int delaySeconds, Action sendMessageAction, CancellationToken token)
        {
            _delaySeconds = delaySeconds;
            _sendMessageAction = sendMessageAction;
            _token = token;
        }

        public async Task IsAlive()
        {
            try
            {
                while (!_token.IsCancellationRequested)
                {
                    await Task.Delay(_delaySeconds * 1000, _token);
                    if (!_token.IsCancellationRequested)
                        _sendMessageAction();
                }
            }
            catch (TaskCanceledException)
            {
                // Clean cancellation
            }
        }

        /*public void Stop()
        {
            _cts?.Cancel();
        }*/
    }
}
