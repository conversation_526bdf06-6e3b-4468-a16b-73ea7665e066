﻿using NLog;
using NLog.Layouts;

namespace Simulateur.Commons
{
    public static class HistoLog
    {
        public static string Log(Logger logger, string message, LogLevel? level = null)
        {
            SimpleLayout _uiLogLayout = new SimpleLayout("${longdate} | ${level:uppercase=true} | ${message}");
            level ??= LogLevel.Info;

            var logEvent = new LogEventInfo(level, logger.Name, message);
            logger.Log(logEvent);   // feed the file

            return _uiLogLayout.Render(logEvent);
        }
    }
}
