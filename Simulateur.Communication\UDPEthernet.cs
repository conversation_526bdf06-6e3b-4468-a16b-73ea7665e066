﻿using System.Text;
using System.Net;
using System.Net.Sockets;
using Simulateur.Commons;
using NLog;

namespace Simulateur.Communication
{
    public class UDPEthernet : IUDPEthernet
    {
        public int ListenPort { get; set; }
        public int WatchDogDelay { get; private set; }
        public UdpClient CurrentUdpClient { get; private set; }
        private CancellationTokenSource _cts;
        private Task _listenerTask;

        public Action<byte[], IPEndPoint> OnMessageReceived { get; set; }

        private WatchDog watchdog;
        private bool IsWatchdogActivated;

        private static readonly Logger logger = LogManager.GetCurrentClassLogger();

        //Constructeurs
        //default port for µMA is 5006
        //!\watchout with default behavior, constructor receive blank (not null) parameters.
        public UDPEthernet(int customPort = 5006, int watchDogDelay = 1, bool isWatchdogActivated = false)
        {
            ListenPort = customPort > 0 ? customPort : 5006;
            WatchDogDelay = watchDogDelay > 0 ? watchDogDelay : 1;
            IsWatchdogActivated = isWatchdogActivated;
        }

        //Méthodes
        public Task Start()
        {
            _cts = new CancellationTokenSource();
            CancellationToken token = _cts.Token;

            _listenerTask = Task.Run(async () =>
            {
                IPEndPoint endPoint = new IPEndPoint(IPAddress.Any, ListenPort);

                CurrentUdpClient = new UdpClient();
                CurrentUdpClient.Client.SetSocketOption(SocketOptionLevel.Socket, SocketOptionName.ReuseAddress, true);
                CurrentUdpClient.Client.Bind(endPoint);

                var bound = (IPEndPoint)CurrentUdpClient.Client.LocalEndPoint!;
                HistoLog.Log(logger, $"UDP bind sur {bound.Address}:{bound.Port}", LogLevel.Info);
                try
                {
                    while (!token.IsCancellationRequested)
                    {
                        try
                        {
                            UdpReceiveResult result = await CurrentUdpClient.ReceiveAsync();
                            OnMessageReceived?.Invoke(result.Buffer, result.RemoteEndPoint);

                            if (IsWatchdogActivated && watchdog == null)
                            {
                                watchdog = new WatchDog(WatchDogDelay, () =>
                                {
                                    SendMessage("1", result.RemoteEndPoint);
                                }, token);

                                _ = Task.Run(() => watchdog.IsAlive());
                            }
                        }
                        catch (ObjectDisposedException)
                        {
                            break;
                        }
                        catch (SocketException e)
                        {
                            HistoLog.Log(logger, $"Socket error: {e.Message}", LogLevel.Error);
                            break;
                        }
                    }
                }
                catch (OperationCanceledException)
                {
                    HistoLog.Log(logger, "UDP listener cancelled.", LogLevel.Info);
                }
                finally
                {
                    CurrentUdpClient?.Dispose();
                    CurrentUdpClient = null;
                }
            });

            return _listenerTask;
        }

        public async Task Stop()
        {
            _cts?.Cancel();
            CurrentUdpClient?.Dispose();
            CurrentUdpClient = null;

            if (_listenerTask != null)
            {
                try
                {
                    await _listenerTask;
                }
                catch (Exception e)
                {
                    HistoLog.Log(logger, $"Listener stop exception: {e.Message}", LogLevel.Warn);
                }
            }

            _cts = null;
        }

        public async Task SendMessage(byte[] payload, IPEndPoint senderEndPoint)
        {
            if (CurrentUdpClient != null && !_cts.Token.IsCancellationRequested)
            {
                await CurrentUdpClient.SendAsync(payload, payload.Length, senderEndPoint);
            }
        }

        //surcharge pour le type string
        public Task SendMessage(string message, IPEndPoint senderEndPoint)
            => SendMessage(System.Text.Encoding.UTF8.GetBytes(message), senderEndPoint);


        private void CallbackReceived(IAsyncResult res)
        {
            if (CurrentUdpClient != null)
            {
                try
                {
                    UdpClient u = ((UdpState)(res.AsyncState)).u;
                    IPEndPoint e = ((UdpState)(res.AsyncState)).e;
                    byte[] received = u.EndReceive(res, ref e);

                    if (received?.Length > 0)
                    {
                        string message = Encoding.UTF8.GetString(received);
                        HistoLog.Log(logger, $"Received: {message}", LogLevel.Debug);
                    }
                }
                catch (Exception e)
                {
                    HistoLog.Log(logger, $"UDP reception error: {e.Message}", LogLevel.Error);
                    HistoLog.Log(logger, $"StackTrace: {e.StackTrace}", LogLevel.Error);
                }
                finally
                {
                    CurrentUdpClient?.BeginReceive(new AsyncCallback(CallbackReceived), (UdpState)res.AsyncState);
                }
            }
        }
    }
}
