# example repository config (see 'hg help config' for more info)
[paths]
default = http://vm-hg-ulis.in-snec.com/hg/_1936

# path aliases to other clones of this repo in URLs or filesystem paths
# (see 'hg help config.paths' for more info)
#
# default:pushurl = ssh://<EMAIL>/hg/jdoes-fork
# my-fork         = ssh://<EMAIL>/hg/jdoes-fork
# my-clone        = /home/<USER>/jdoes-clone

[ui]
# name and email (local to this repository, optional), e.g.
# username = <PERSON> <<EMAIL>>
