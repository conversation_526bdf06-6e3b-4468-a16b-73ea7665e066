﻿// See https://aka.ms/new-console-template for more information
//Console.WriteLine("Hello, World!");
using System.Net;
using System.Net.Sockets;
using MicroMa.MicroMaProtocol.Messages.V1.ControlCommand;
using MicroMa.MicroMaProtocol.Messages.V1.Dataloading;
using MicroMa.MicroMaProtocol.Messages.V1.Discovery;
using MicroMa.MicroMaProtocol.Messages.V1.Monitoring;
using MicroMa.MicroMaProtocol.Messages.V1.Test;

const string udpIp = "127.0.0.1";
const int udpPort = 5006;
const int timeoutSeconds = 5;

using var udpClient = new UdpClient();
udpClient.Connect(udpIp, udpPort);
udpClient.Client.ReceiveTimeout = timeoutSeconds * 1000;

var req = new ReqMccKeepAliveV1();
byte[] message = req.WriteBin();

/*
ReqCloseDirV1 req2 = new ReqCloseDirV1();
byte[] message2 = req2.WriteBin();
*/

await udpClient.SendAsync(message, message.Length);
Console.WriteLine($"Envoi : {BitConverter.ToString(message)} -> {udpIp}:{udpPort}");

try
{
    IPEndPoint remote = new IPEndPoint(IPAddress.Any, 0);
    byte[] buffer = udpClient.Receive(ref remote);
    Console.WriteLine($"Réponse reçue de {remote}: {BitConverter.ToString(buffer)}");
}
catch (SocketException ex) when (ex.SocketErrorCode == SocketError.TimedOut)
{
    Console.WriteLine("Timeout de réception.");
}
finally
{
    udpClient.Close();
    Console.WriteLine("Connexion fermée.");
    Console.ReadKey();
}
