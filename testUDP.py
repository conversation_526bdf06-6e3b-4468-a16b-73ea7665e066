import socket
import time

# Définition de l'adresse et du port du serveur
UDP_IP = "127.0.0.1"
UDP_PORT = 5000

client_socket = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
client_socket.settimeout(5)  # Timeout pour la réception

message = "REQ_OPEN_FILE".encode('utf-8')
print(f"📨 Envoi : {message.decode()} à {UDP_IP}:{UDP_PORT}")

try:
    # Envoi du message
    client_socket.sendto(message, (UDP_IP, UDP_PORT))

    start_time = time.time()  # Démarrer le chronomètre

    while time.time() - start_time < 10:  # Maintenir la connexion 10s
        try:
            response, server_address = client_socket.recvfrom(1024)
            print(f"📩 Réponse reçue du serveur : {response.decode()}")
        except socket.timeout:
            print("⏳ Aucun retour du serveur après 5 secondes, attente d'une autre réponse...")

finally:
    client_socket.close()
    print("🔴 Connexion fermée.")
