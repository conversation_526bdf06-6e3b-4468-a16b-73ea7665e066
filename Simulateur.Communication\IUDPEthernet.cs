﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Net.Sockets;
using System.Net;
using System.Text;
using System.Threading.Tasks;

namespace Simulateur.Communication
{
    public interface IUDPEthernet
    {
        int ListenPort { get; }
        UdpClient CurrentUdpClient { get; }
        Action<byte[], IPEndPoint> OnMessageReceived { get; set; }

        Task Start();
        Task SendMessage(byte[] responseMessage, IPEndPoint senderEndPoint);
        Task Stop();
    }
}
