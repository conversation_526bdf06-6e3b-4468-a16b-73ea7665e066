@startuml
title Architecture du Simulateur AIM

skinparam packageStyle rectangle
skinparam classAttributeIconSize 0

package "IHM_Simulateur (WPF)" {
  interface ICoreMicroMA {
    +Start()
    +InitResponses(module : string, reponses : List<byte[]>)
    +Stop()
  }

  class MainViewModel {
    -ExecuteStart()
    -LogToUI(logger : logger, message : string, importance : LogLevel)
    -ApplyParsedKeyValues()
    -OpenOptions()
    -Stop()
  }

  class OptionsViewModel {}

  class Views <<XAML>> {
  }
}

package "Core_Block" {
  class Core {
    -serviceUDP : IUDPEthernet
    +Start()
    +Stop()
    +InitResponses(module : string, reponses : List<byte[]>)
    +Processing(payload : byte[]) : byte[]
    +SendMessage(payload : byte[], target : IPEndPoint)
  }
}

package "Communication_Block" {
  interface IUDPEthernet {
    +Start()
    +Stop()
    +SendMessage(payload : byte[], target : IPEndPoint) : Task
  }

  class UDPEthernet implements IUDPEthernet {
    +Start()
    +Stop()
    +SendMessage(payload : byte[], target : IPEndPoint) : Task
  }
}

package "Dictionary_Bloc" {
  class Dictionary {
    {static} +Instance : Dictionary
    -LoadDefauts()
    +ResetDefault()
    +GetResponse(key : string) : byte[]
    +ResetEmpty()
  }

  class CountedResponse {
    +SetResponses(Reponses : List<byte[]>)
    +GetNextResponses() : byte[]
  }
  enum EnumCommandList
}

package "GlobalConf_Block" {
  class GlobalConf {
    {static} +Instance : GlobalConf
    +CommunicationPort : int
    +ModuleSimulated : string
    +Delay : int
    +CommunicationService : string
    +IsWatchdogActivated : bool
    +WatchdogDelay : int
  }
}

package "Commons_Block" {
  class Commons {
    +Log(logger, msg : string, level : LogLevel)
  }
}

ICoreMicroMA <|.. Core
MainViewModel --> Core : appelle

OptionsViewModel --> GlobalConf : charge/sauve
Views --> MainViewModel : data binding

Core --> IUDPEthernet : utilise
Core --> Dictionary : requête réponse
Core --> GlobalConf : lit paramètres
Core --> Commons : logging
UDPEthernet --> Commons : logging

CountedResponse --> Dictionary : intègre
EnumCommandList --> CountedResponse


@enduml
