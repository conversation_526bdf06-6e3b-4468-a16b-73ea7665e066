﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Simulateur.Dictionnary
{
    /// <summary>
    /// La classe <c>DictionnaireMicroMA</c> implémente un dictionnaire, initialisé vide, de réponse dépendement du type de message reçu.
    /// Elle possède des méthodes pour définir les réponses attendues de chaque type de message, et une méthode pour récuperer l'ensemble des réponses d'un type de message.
    /// </summary>
    /// <typeparam name="T">Type Template pour le type de réponse du message</typeparam>
    public class DictionnaireMicroMA<T>
    {
        private static DictionnaireMicroMA<T>? _instance;
        private static readonly object _lock = new object();
        private Dictionary<string, CountedResponse<T>> _responses;

        public static DictionnaireMicroMA<T> Instance
        {
            get
            {
                if (_instance == null)
                {
                    lock (_lock)
                    {
                        if (_instance == null)
                        {
                            _instance = new DictionnaireMicroMA<T>();
                        }
                    }
                }
                return _instance;
            }
        }
        private DictionnaireMicroMA()
        {
            _responses = new Dictionary<string, CountedResponse<T>>();
            foreach (EnumCommandList type in Enum.GetValues(typeof(EnumCommandList)))
            {
                _responses[type.ToString()] = new CountedResponse<T>();
            }
        }

        /// <summary>
        /// Initialise les messages associé à la clé <c>key</c> avec la liste <c>InputResponses</c>
        /// </summary>
        public void SetResponses(string key, List<T> InputResponses)
        {
            /*if (!Enum.IsDefined(typeof(EnumCommandList), key))
                throw new KeyNotFoundException($"{key} is not a defined command.");*/

            //Si la clé existe deja, mise a jour des reponses
            if (_responses.ContainsKey(key))
            {
                _responses[key].SetResponses(InputResponses);
            }
            else
            {
                throw new KeyNotFoundException($"{key} is not a defined command.");
            }
        }

        /// <summary>
        /// Retourne le prochain message définit dans la liste des réponses
        /// </summary>
        public T GetResponse(string key)
        {
            if (_responses.TryGetValue(key, out CountedResponse<T> obj))
            {
                return obj.GetNextResponses();
            }
            return default(T);
        }

        public void Reset()
        {
            _responses.Clear(); // on vide tout
            foreach (EnumCommandList type in Enum.GetValues(typeof(EnumCommandList)))
            {
                _responses[type.ToString()] = new CountedResponse<T>(); // on réinitialise proprement tout
            }
        }


    }

}

