﻿using Microsoft.VisualStudio.TestTools.UnitTesting;
using Simulateur.Communication;
using Simulateur.Core;
using Simulateur.Dictionnary;
using System;
using System.Collections.Generic;
using System.Net;
using System.Net.Sockets;
using System.Text;

namespace Simu.Tests
{
    [TestClass]
    public class CoreTests
    {
        [TestInitialize]
        public void ResetBeforeEachTest()
        {
            DictionnaireMicroMA<byte[]>.Instance.ResetEmpty();
        }

        [TestMethod]
        public void InitResponses_ShouldStoreResponsesCorrectly()
        {
            string key = "REQ_OPEN_FILE";
            byte[] resp1 = [0, 16, 104, 213];
            byte[] resp2 = [4, 1, 14, 21];
            var expected = new List<byte[]> { resp1, resp2 };

            Core.Instance.InitResponses(key, expected);
            var dico = DictionnaireMicroMA<byte[]>.Instance;

            Assert.AreEqual(resp1, dico.GetResponse(key));
            Assert.AreEqual(resp2, dico.GetResponse(key));
        }

        [TestMethod]
        public void InitResponses_ShouldCycleResponses()
        {
            string key = "REQ_OPEN_FILE";
            byte[] resp1 = [0, 16, 104, 213];
            byte[] resp2 = [4, 1, 14, 21];
            var responses = new List<byte[]> { resp1, resp2 };

            Core.Instance.InitResponses(key, responses);
            var dico = DictionnaireMicroMA<byte[]>.Instance;

            Assert.AreEqual(resp1, dico.GetResponse(key));
            Assert.AreEqual(resp2, dico.GetResponse(key));
            Assert.AreEqual(resp1, dico.GetResponse(key)); // cycle
            Assert.AreEqual(resp2, dico.GetResponse(key)); // cycle
        }

        //this test doesn't require to change to byte[], we're looking for his behavior
        [TestMethod]
        [ExpectedException(typeof(KeyNotFoundException))]
        public void InitResponses_ShouldThrowIfKeyInvalid()
        {
            var dico = DictionnaireMicroMA<byte[]>.Instance;
            dico.SetResponses("REQ_FAKE", new List<byte[]> { });
        }

        [TestMethod]
        public void SendMessage_ShouldInvokeCallback()
        {
            // Arrange
            byte[] message = [1, 2, 3, 4];
            string received = null;
            Core.OnMessageReceived = (msg) => received = msg;

            var endpoint = new IPEndPoint(IPAddress.Loopback, 5000);

            // Injection d’un faux serviceUDP
            var fakeUDP = new FakeUDPService();
            typeof(Core).GetField("serviceUDP", System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance)
                .SetValue(Core.Instance, fakeUDP);

            // Act
            Core.Instance.GetType()
                .GetMethod("SendMessage", System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance)
                .Invoke(Core.Instance, new object[] { message, endpoint });

            // Assert
            Assert.AreEqual(BitConverter.ToString(message), received);
        }

        private class FakeUDPService : IUDPEthernet
        {
            public Action<byte[], IPEndPoint> OnMessageReceived { get; set; }

            public int ListenPort => throw new NotImplementedException();
            public UdpClient CurrentUdpClient => throw new NotImplementedException();

            public Task SendMessage(byte[] msg, IPEndPoint ip)
            {
                return null;
            }

            public Task Start() => Task.CompletedTask;

            public Task Stop() => Task.CompletedTask;
        }


    }
}
