@startuml Simulateur

interface IUDPEthernet
interface ICoreMicroMA {
    Start()
    InitResponses(str, List<string>)
    Stop()
}

class Core {
    {static} Core Instance

    Start()
    InitResponses(str, List<string>)
    Processing(byte[])
    SendMessage(str, IPEndPoint)
    Stop()
}

class Dictionary {
    +{static}Dictionary.Instance
    +GetResponse(str)
    +SetRsp(str, List<T>)
}

class GlobalConf {
    +Config.Instance
    +int CommunicationPort
    +string ModuleSimulated
    +int Delay
    +bool Debug
    +string CommunicationService
    +bool IsWatchdogActivated
    +int WatchDogDelay
}

class Commons {
    {static} Log(Logger, string, LogLevel)
}

class Communication {
    +Start()
    +SendMsg(string, IPEndPoint)
    +Stop()
}

class IHM

' Relations
IHM --> ICoreMicroMA

Dictionary --> Core
ICoreMicroMA ..|> Core

Commons --> Communication 
Commons --> Core

GlobalConf --> Communication 
GlobalConf --> Core

Core --> IUDPEthernet
Communication --> IUDPEthernet
'IUDPEthernet --> Communication

@enduml
