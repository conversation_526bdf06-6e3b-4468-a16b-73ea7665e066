﻿using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.ComponentModel;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Input;
using CommunityToolkit.Mvvm.ComponentModel;
using CommunityToolkit.Mvvm.Input;
using IHM_Simulateur.Interfaces;
using IHM_Simulateur.Model;
using Simulateur.Commons;
using Simulateur.Core;

namespace IHM_Simulateur.ViewModel
{
    public class MainViewModel : INotifyPropertyChanged
    {
        /*Paramaters*/
        Dictionary<string, string> parameters = new Dictionary<string, string>
            {
                { "CommunicationPort", "5000" },
                { "StreamIp", "127.0.0.1" },
                { "Delay", "3" },
            };
        private int _port = 5000;
        private int _delai = 3;
        public int Port
        {
            get => _port;
            set
            {
                if (_port != value)
                {
                    _port = value;
                    parameters["CommunicationPort"] = _port.ToString();
                    OnPropertyChanged(nameof(Port));
                    LogText += $"Port mis à jour: {_port}\n";

                    OnParametersChanged();
                }
            }
        }
        public int Delai
        {
            get => _delai;
            set
            {
                if (_delai != value)
                {
                    _delai = value;
                    parameters["Delay"] = _delai.ToString();
                    OnPropertyChanged(nameof(Delai));
                    LogText += $"Delai mis à jour: {_delai}\n";

                    OnParametersChanged();
                }
            }
        }

        /*Constructor*/
        private ICoreMicroMA _core;
        public AppModel Model { get; private set; }
        public ICommand ToggleSwitchCommand { get; }
        public ObservableCollection<string> OptionsList { get; set; }

        //ajoute des interfaces en parametre au constructeur
        public MainViewModel(ICoreMicroMA c, AppModel m)
        {
            _core = c;
            Model = m;
            ToggleSwitchCommand = new RelayCommand<object>(ToggleSwitch);

            OptionsList = new ObservableCollection<string>
            {
            "MicroMA",
            "XMA"
            };
            SelectedOption = OptionsList[0];
        }

        /*LogText & Switch*/
        public string LogText
        {
            get => Model.LogText;
            set
            {
                Model.LogText = value;
                OnPropertyChanged(nameof(LogText));
            }
        }

        public bool Switch1
        {
            get => Model.Switch1;
            set
            {
                if (Model.Switch1 != value)
                {
                    Model.Switch1 = value;
                    OnPropertyChanged(nameof(Switch1));

                    Port = value ? 5000 : 5001;

                    //Restart server, if started
                    if (IsSimulatorActivated)
                    {
                        _core.Stop();
                        _core.Start();
                    }
                }
            }
        }

        public bool Switch2
        {
            get => Model.Switch2;
            set
            {
                if (Model.Switch2 != value)
                {
                    Model.Switch2 = value;
                    OnPropertyChanged(nameof(Switch2));

                    Delai = value ? 3 : 1;
                }
            }
        }

        public bool Switch3
        {
            get => Model.Switch3;
            set
            {
                Model.Switch3 = value;
                OnPropertyChanged(nameof(Switch3));
            }
        }

        public bool Switch4
        {
            get => Model.Switch4;
            set
            {
                Model.Switch4 = value;
                OnPropertyChanged(nameof(Switch4));
            }
        }

        public string SelectedOption
        {
            get { return _selectedOption; }
            set
            {
                if (_selectedOption != value)
                {
                    _selectedOption = value;
                    OnPropertyChanged(nameof(SelectedOption));
                    UpdateParametersForSelectedOption();
                }

            }
        }

        public bool IsSimulatorActivated
        {
            get => Model.IsSimulatorActivated;
            set
            {
                if (Model.IsSimulatorActivated != value)
                {
                    Model.IsSimulatorActivated = value;
                    OnPropertyChanged(nameof(IsSimulatorActivated));

                    if (value)
                    {
                        LogText += "Serveur lancé !\n";
                        _ = ExecuteStart();
                    }
                    else
                    {
                        _core.Stop();
                        LogText += "Serveur éteint !\n";
                    }
                }
            }
        }

        /*Methodes*/
        private async Task ExecuteStart()
        {
            Core.OnMessageReceived = (msg) => LogText += msg;
            _core.Start();
        }

        private void ToggleSwitch(object parameter)
        {
            if (parameter is string switchName)
            {
                var property = GetType().GetProperty(switchName);
                if (property != null && property.PropertyType == typeof(bool))
                {
                    bool currentValue = (bool)property.GetValue(this);
                    property.SetValue(this, !currentValue);

                    LogText += $"{switchName} : {(!currentValue ? "ON" : "OFF")}\n";
                }
            }
        }
        private void UpdateParametersForSelectedOption()
        {
            switch (SelectedOption)
            {
                case "microMA":
                    //insérer ici les paramètres correspondants
                    LogText += "Configuration microMA appliquée\n";
                    break;

                case "XMA":
                    //insérer ici les paramètres correspondants

                    LogText += "Configuration XMA appliquée\n";
                    break;

                default:
                    LogText += "Option inconnue sélectionnée\n";
                    break;
            }

            // Redémarrer le simulateur si actif
            if (IsSimulatorActivated)
            {
                _core.Stop();
                _core.Start();
                LogText += "Serveur redémarré !\n";

            }
        }


        private void OnParametersChanged()
        {
            var paramCopy = new Dictionary<string, string>(parameters);
            /*_core.UpdateParameters(paramCopy);*/
        }


        public event PropertyChangedEventHandler? PropertyChanged;
        protected void OnPropertyChanged(string propertyName)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }
    }
}
