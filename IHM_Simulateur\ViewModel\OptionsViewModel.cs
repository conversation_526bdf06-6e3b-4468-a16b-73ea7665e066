﻿using CommunityToolkit.Mvvm.ComponentModel;
using CommunityToolkit.Mvvm.Input;
using Simulateur.Commons;
using System;
using System.ComponentModel;
using System.Windows;
using System.Collections.Generic;
using Simulateur.Core;
using NLog;
using Simulateur.GlobalConf;

namespace IHM_Simulateur.ViewModel
{
    public class OptionsViewModel : ObservableObject, IDataErrorInfo
    {
        ICoreMicroMA _core = Core.Instance;

        // Propriétés bindées dans la fenêtre Options
        public string ModuleName { get; set; }      // Module simulé sélectionné
        public List<string> ListModules { get; set; } // Liste des modules disponibles dans le menu déroulant
        public int Delay { get; set; }              // Délai de réponse simulé
        public string LogFilePath { get; set; }           // Chemin relatif (au .exe) ou se trouve les logs
        public bool Debug { get; set; }             // Mode debug activé ?
        public bool IsWatchdogActivated { get; set; }     // Watchdog actif ?
        public int WatchDogDelay { get; set; }            // D<PERSON>lai du watchdog
        public RelayCommand ApplyCommand { get; }         // Commande du bouton "Valider"

        public OptionsViewModel()
        {
            // Initialisation des listes déroulantes
            ListModules = new List<string> { "MicroMA", "XMA", "MDR" };

            // Chargement des valeurs actuelles depuis la configuration globale
            var config = ConfigSimu.Instance;
            //Delai en ms
            Delay = config.Delay;
            Debug = config.Debug;
            ModuleName = config.ModuleSimulated;
            LogFilePath = config.LogFilePath;

            ApplyCommand = new RelayCommand(ApplySettings);
        }

        // Applique les paramètres dans la config globale si tout est valide
        private void ApplySettings()
        {
            if (!IsValid())
            {
                MessageBox.Show("Certains champs sont invalides.", "Erreur", MessageBoxButton.OK, MessageBoxImage.Warning);
                return;
            }

            var config = ConfigSimu.Instance;

            config.Delay = Delay;
            config.Debug = Debug;
            config.ModuleSimulated = ModuleName;

            //Possiblement reprendre la possibilité de pouvoir modifié le port ?
            //_core.UpdatePort(CommunicationPort);

            config.LogFilePath = LogFilePath;
            _core.SetLogFileName(LogFilePath);

            HistoLog.Log(LogManager.GetCurrentClassLogger(), $"Changement du path from : {LogFilePath}\n to :{config.LogFilePath}", LogLevel.Info);

            Application.Current.Windows[Application.Current.Windows.Count - 1]?.Close();
        }

        // Vérifie que tous les champs critiques sont valides
        private bool IsValid()
        {
            return string.IsNullOrEmpty(this[nameof(Delay)]) &&
                   string.IsNullOrEmpty(this[nameof(WatchDogDelay)]);
        }

        // Interface IDataErrorInfo : non utilisée ici
        public string? Error => null;

        // Validation champ par champ (bindings WPF automatiques)
        public string? this[string columnName]
        {
            get
            {
                switch (columnName)
                {
                    case nameof(Delay):
                        return Delay < 0 ? "Le délai doit être positif." : null;
                    case nameof(WatchDogDelay):
                        return WatchDogDelay < 0 ? "Le délai du watchdog doit être positif." : null;
                    default:
                        return null;
                }
            }
        }
    }
}
