﻿using Microsoft.VisualStudio.TestTools.UnitTesting;
using Simulateur.Commons;
using System;
using System.Reflection;
using NLog;
using Simulateur.GlobalConf;

namespace Simu.Tests
{
    [TestClass]
    public class CommonsTests
    {
        [TestMethod]
        public void ConfigSimu_Instance_ShouldBeSingleton()
        {
            var instance1 = ConfigSimu.Instance;
            var instance2 = ConfigSimu.Instance;

            Assert.AreSame(instance1, instance2);
        }

        [TestMethod]
        public void ConfigSimu_DefaultValues_ShouldBeSet()
        {
            var config = ConfigSimu.Instance;

            Assert.AreEqual("MicroMA", config.ModuleSimulated);
            Assert.AreEqual(1, config.Delay);
            Assert.AreEqual(false, config.Debug);
            /*Assert.AreEqual("UDP", config.CommunicationService);
            Assert.AreEqual(true, config.IsWatchdogActivated);
            Assert.AreEqual(1, config.WatchDogDelay);*/
        }

        [TestMethod]
        public void HistoLog_ShouldReturnFormattedLog()
        {
            var logger = LogManager.GetLogger("TestLogger");
            string message = "This is a test log.";

            string result = HistoLog.Log(logger, message, LogLevel.Warn);

            Assert.IsTrue(result.Contains("WARN"));
            Assert.IsTrue(result.Contains("This is a test log."));
        }

        [TestMethod]
        public void HistoLog_ShouldDefaultToInfoLogLevel()
        {
            var logger = LogManager.GetLogger("TestLogger");
            string message = "Message with default level";

            string result = HistoLog.Log(logger, message);

            Assert.IsTrue(result.Contains("INFO"));
        }
    }
}
