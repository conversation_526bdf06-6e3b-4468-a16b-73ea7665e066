﻿<Project Sdk="Microsoft.NET.Sdk">

	<PropertyGroup>
		<OutputType>WinExe</OutputType>
		<TargetFramework>net9.0-windows</TargetFramework>
		<Nullable>enable</Nullable>
		<ImplicitUsings>enable</ImplicitUsings>
		<UseWPF>true</UseWPF>
	</PropertyGroup>

	<ItemGroup>
		<None Remove="nlog.config" />
		<None Remove="Ressources\Images\logo.ico" />
	</ItemGroup>

	<ItemGroup>
		<PackageReference Include="CommunityToolkit.Mvvm" Version="8.4.0" />
		<PackageReference Include="Microsoft.Extensions.DependencyInjection" Version="9.0.3" />
		<PackageReference Include="NLog.Extensions.Logging" Version="5.4.0" />
	</ItemGroup>

	<ItemGroup>
		<Content Include="nlog.config">
		  <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
		</Content>
		<Content Include="Ressources\Images\logo.ico">
			<CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
		</Content>
	</ItemGroup>

	<ItemGroup>
		<Folder Include="Services\" />
	</ItemGroup>

	<ItemGroup>
	  <ProjectReference Include="..\Simulateur.Commons\Simulateur.Commons.csproj" />
	  <ProjectReference Include="..\Simulateur.Core\Simulateur.Core.csproj" />
	  <ProjectReference Include="..\Simulateur.GlobalConf\Simulateur.GlobalConf.csproj" />
	</ItemGroup>

	<ItemGroup>
	  <Compile Update="Properties\Settings.Designer.cs">
	    <DesignTimeSharedInput>True</DesignTimeSharedInput>
	    <AutoGen>True</AutoGen>
	    <DependentUpon>Settings.settings</DependentUpon>
	  </Compile>
	</ItemGroup>

	<ItemGroup>
	  <None Update="Properties\Settings.settings">
	    <Generator>SettingsSingleFileGenerator</Generator>
	    <LastGenOutput>Settings.Designer.cs</LastGenOutput>
	  </None>
	</ItemGroup>

</Project>
