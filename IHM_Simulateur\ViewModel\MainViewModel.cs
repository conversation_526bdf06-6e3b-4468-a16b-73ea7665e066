﻿using System.Collections.ObjectModel;
using System.ComponentModel;
using System.Text.RegularExpressions;
using System.Windows.Input;
using CommunityToolkit.Mvvm.Input;
using IHM_Simulateur.Model;
using NLog;
using Simulateur.Commons;
using Simulateur.Core;
using Simulateur.GlobalConf;

using ResponseDico = Simulateur.Dictionnary.DictionnaireMicroMA<byte[]>;


namespace IHM_Simulateur.ViewModel
{
    public class MainViewModel : INotifyPropertyChanged
    {
        private static readonly Logger logger = LogManager.GetCurrentClassLogger();

        // Collection des logs à afficher dans l'IHM
        public ObservableCollection<string> LogEntries { get; } = new ObservableCollection<string>();

        // Représentation sous forme de texte unique pour le TextBox
        public string JoinedLogEntries => string.Join(System.Environment.NewLine, LogEntries);

        ConfigSimu parameters = ConfigSimu.Instance;

        // D<PERSON>lai de réponse simulé entre requête et réponse
        private int _delayValue = 1;
        public int DelayValue
        {
            get
            {
                LogToUI(logger, $"Délai : {_delayValue}", LogLevel.Debug);
                return _delayValue;
            }
            set
            {
                if (_delayValue != value)
                {
                    _delayValue = value;
                    OnPropertyChanged(nameof(DelayValue));
                }
            }
        }


        private string _keyValueInputRaw = string.Empty;
        public string KeyValueInputRaw
        {
            get => _keyValueInputRaw;
            set
            {
                if (_keyValueInputRaw != value)
                {
                    _keyValueInputRaw = value;
                    OnPropertyChanged(nameof(KeyValueInputRaw));
                }
            }
        }

        private readonly ICoreMicroMA _core; // Simulateur injecté
        public AppModel Model { get; private set; }

        // Commandes liées à l'IHM
        public ICommand ToggleSwitchCommand { get; }
        public ICommand OpenOptionsCommand { get; }
        public ICommand ToggleSimulationCommand { get; }
        public ICommand ApplyKeyValueCommand { get; }

        public MainViewModel(ICoreMicroMA c, AppModel m)
        {
            _core = c;
            Model = m;
            ToggleSwitchCommand = new RelayCommand<object>(ToggleSwitch);
            OpenOptionsCommand = new RelayCommand(OpenOptions);
            ToggleSimulationCommand = new RelayCommand(ToggleSimulation);
            ApplyKeyValueCommand = new RelayCommand(ApplyParsedKeyValues);
        }

        // Switch qui change le port de communication (ex: 5000/5001)
        public bool Switch1
        {
            get => Model.Switch1;
            set
            {
                if (Model.Switch1 != value)
                {
                    Model.Switch1 = value;
                    OnPropertyChanged(nameof(Switch1));
                }
            }
        }

        // Indique si le simulateur est actif ou non
        public bool IsSimulatorActivated
        {
            get => Model.IsSimulatorActivated;
            set
            {
                if (Model.IsSimulatorActivated != value)
                {
                    Model.IsSimulatorActivated = value;
                    OnPropertyChanged(nameof(IsSimulatorActivated));
                    if (value)
                    {
                        _ = ExecuteStart();
                        LogToUI(logger, "La fonction Start() s'exécute !", LogLevel.Info);
                    }
                    else
                    {
                        _core.Stop();
                        LogToUI(logger, "Serveur éteint\n", LogLevel.Info);
                    }
                }
            }
        }

        // Redémarre le simulateur si actif (utile après changement de paramètres)
        private void RestartSimulatorIfActive()
        {
            if (IsSimulatorActivated)
            {
                _core.Stop();
                _core.Start();
                LogToUI(logger, "Simulateur redémarré avec la nouvelle configuration.", LogLevel.Info);
            }
        }

        // Exécution asynchrone du démarrage du simulateur
        private Task ExecuteStart()
        {
            Core.OnMessageReceived = (msg) =>
            {
                LogEntries.Add(msg);
                OnPropertyChanged(nameof(JoinedLogEntries));
            };
            _core.Start();
            return Task.CompletedTask;
        }

        // Ajoute un log à la fois dans le fichier et dans l'IHM
        private void LogToUI(Logger logger, string message, LogLevel level)
        {
            string formattedLog = HistoLog.Log(logger, message, level);
            LogEntries.Add(formattedLog);
            OnPropertyChanged(nameof(JoinedLogEntries));
        }

        private void ApplyParsedKeyValues()
        {
            if (string.IsNullOrWhiteSpace(KeyValueInputRaw))
                return;

            var lines = KeyValueInputRaw.Split(new[] { '\r', '\n' }, StringSplitOptions.RemoveEmptyEntries);

            foreach (var line in lines)
            {
                try
                {
                    // Exemple attendu : REQ_CMD => { "val1", "val2" }
                    /* \"([A-Z_]+)\" : La clé doit être en MAJUSCULE avec des _,
                     * \\s*=>\\s* : Le token '=>' est entouré, ou pas, d'espace
                     * \\{ : La liste des réponses commence par une accolade
                     * (?: : Défini le groupe entre parenthèse comme non capturé
                     * \"[^\"]*\" : Récupère tout les mots qui ne contiennent pas de guillemets, mais qui sont entre guillemets
                     * (?:\\s*,?\\s*)?)* : Sélectionne mais ne capture pas les
                     * espace avec virgule optionnel, espace et possibilité d'avoir aucune réponses
                     * \\} : La liste des réponses se fini par une accolade
                     */

                    //Exemple : REQ_OPEN_FILE => {"[64],[102],[64],[102]","[1],[84],[74],[054]"}
                    //Exemple : ANS_CELL_CONF => {"[16],[0], [0],[29],[0],[0],[255],[255],[255],[254],[0],[0],[0],[0],[0],[0],[128],[0],[1],[1],[0],[0],[0],[0],[0],[0],[0],[0],[0]"}
                    var keyRegex = Regex.Match(
                        line,
                        "([A-Z_]+)\\s*=>"
                    );
                    var valuesGroupRegex = Regex.Match(
                        line,
                        "=>\\s*{\\s*(.*?)\\s*}"
                    );

                    //LogToUI(logger, "Key & Values is valid : " + (key.Success && valuesGroup.Success).ToString(), LogLevel.Debug);
                    //LogToUI(logger, "Values is valid : " + valuesGroupRegex.Success, LogLevel.Debug);
                    //LogToUI(logger, "Key is valid : " + keyRegex.Success, LogLevel.Debug);
                    if (keyRegex.Success && valuesGroupRegex.Success)
                    {
                        string key = keyRegex.Groups[1].Value;
                        string valuesGroup = valuesGroupRegex.Groups[1].Value;

                        //LogToUI(logger, "Valeurs associées : " + valuesGroupRegex.Groups[1].Value, LogLevel.Debug);
                        //LogToUI(logger, "Clé trouvé valant : " + key, LogLevel.Debug);

                        // On split chaque groupe (entre guillemets)
                        var byteGroups = Regex.Matches(valuesGroup, "\"([^\"]+)\"")
                            .Cast<Match>()
                            .Select(m => m.Groups[1].Value)
                            .ToList();

                        var responses = new List<byte[]>();

                        foreach (var group in byteGroups)
                        {
                            var bytes = Regex.Matches(group, @"\[(\d+)\]")
                                .Cast<Match>()
                                .Select(m => m.Groups[1].Value)
                                .Select(s => byte.TryParse(s, out var b) ? (byte?)b : null)
                                .ToArray();

                            if (bytes.All(b => b.HasValue))
                            {
                                responses.Add(bytes.Select(b => b!.Value).ToArray());
                            }
                            else
                            {
                                LogToUI(logger, $"Groupe ignoré (format invalide) : {group}", LogLevel.Warn);
                            }
                        }

                        if (responses.Count > 0)
                        {
                            ResponseDico.Instance.SetResponses(key, responses);
                            LogToUI(logger, $"Injecté: {key} => {responses.Count} message(s) de 32 bits", LogLevel.Info);
                        }
                        else
                        {
                            LogToUI(logger, $"Aucune réponse 32 bits valide trouvée pour {key}", LogLevel.Warn);
                        }
                    }
                    else
                    {
                        LogToUI(logger, $"Syntaxe invalide : {line}", LogLevel.Warn);
                    }
                }
                catch (Exception ex)
                {
                    LogToUI(logger, $"Erreur parsing: {ex.Message}", LogLevel.Error);
                }
            }
        }



        // Méthode déclenchée par tous les switches
        private void ToggleSwitch(object? parameter)
        {
            if (parameter is string switchName)
            {
                var property = GetType().GetProperty(switchName);
                if (property != null && property.PropertyType == typeof(bool))
                {
                    bool currentValue = (bool)(property.GetValue(this) ?? false);
                    property.SetValue(this, !currentValue);
                    LogToUI(logger, $"{switchName} : {(!currentValue ? "ON" : "OFF")}", LogLevel.Info);
                    //TODO - make sure getResponse has smth to return for logging
                    var response = ResponseDico.Instance.GetResponse("REQ_OPEN_FILE");
                    if (response != null)
                    {
                        LogToUI(logger, $"Retour (en hexa) stocké dans le dictionnaire de la clé REQ_OPEN_FILE : " +
                                        $"{BitConverter.ToString(response)}", LogLevel.Info);
                    }
                }
            }
        }

        // Bascule manuelle du simulateur (commandée par les boutons START/STOP)
        private void ToggleSimulation()
        {
            IsSimulatorActivated = !IsSimulatorActivated;
        }

        // Ouvre la fenêtre des options
        private void OpenOptions()
        {
            var optionsWindow = new View.OptionsWindow();
            optionsWindow.Owner = App.Current.MainWindow;
            optionsWindow.ShowDialog();
        }

        public event PropertyChangedEventHandler? PropertyChanged;
        protected void OnPropertyChanged(string propertyName)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }
    }
}
