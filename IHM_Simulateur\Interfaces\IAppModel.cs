﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace IHM_Simulateur.Interfaces
{
    internal interface IAppModel
    {
        public string LogText { get; set; }
        public bool Switch1 { get; set; }
        public bool Switch2 { get; set; }
        public bool Switch3 { get; set; }
        public bool Switch4 { get; set; }
        public bool IsSimulatorActivated { get; set; }
    }
}
