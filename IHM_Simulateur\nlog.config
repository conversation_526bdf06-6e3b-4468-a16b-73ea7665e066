<?xml version="1.0" encoding="utf-8" ?>
<!-- XSD manual extracted from package NLog.Schema: https://www.nuget.org/packages/NLog.Schema-->
<nlog xmlns="http://www.nlog-project.org/schemas/NLog.xsd"
      xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
      autoReload="true"
	  internalLogFile="c:\temp\skylogs\nlog-internal.log"
      internalLogLevel="Info" >

	<!-- the targets to write to -->
	<targets>
		<!-- write logs in external file-->
		<target xsi:type="File" name="file" fileName="${basedir}/logs/log_${date:format=yyyyMMdd}.txt"
			layout="${longdate} | ${level:uppercase=true} | ${message}" />

		<!-- write logs to the console-->
		<target xsi:type="Debug" name="vsdebug" />
		<!-- binding logs to the front-->
		<target xsi:type="Memory" name="memory"
			layout="${longdate} | ${level:uppercase=true} | ${message}"/>
	</targets>

	<!-- rules to map from logger name to target -->
	<rules>
		<logger name="*" minlevel="Trace" writeTo="file" />
		<logger name="*" minlevel="Info" writeTo="vsdebug, memory" />
		<!-- ORDER FROM MAX TO MIN : Fatal, Error, Warn, Info, Debug, Trace-->
	</rules>
</nlog>
