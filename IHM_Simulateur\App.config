﻿<?xml version="1.0" encoding="utf-8" ?>
<configuration>
	<configSections>
		<sectionGroup name="applicationSettings" type="System.Configuration.ApplicationSettingsGroup, System, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" >
			<section name="IHM_Simulateur.Properties.Settings" type="System.Configuration.ClientSettingsSection, System, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" requirePermission="false" />
		</sectionGroup>
	</configSections>
	<applicationSettings>
		<IHM_Simulateur.Properties.Settings>
   <setting name="Delay" serializeAs="String">
    <value>1</value>
   </setting>
   <setting name="Debug" serializeAs="String">
    <value>False</value>
   </setting>
   <setting name="ModuleSimulated" serializeAs="String">
    <value>MicroMA</value>
   </setting>
   <setting name="CommunicationService" serializeAs="String">
    <value>Ethernet</value>
   </setting>
   <setting name="IsWatchdogActivated" serializeAs="String">
    <value>True</value>
   </setting>
   <setting name="WatchDogDelay" serializeAs="String">
    <value>1</value>
   </setting>
   <setting name="LogFilePath" serializeAs="String">
    <value>${basedir}</value>
   </setting>
  </IHM_Simulateur.Properties.Settings>
	</applicationSettings>
</configuration>