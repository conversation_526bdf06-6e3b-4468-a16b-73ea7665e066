﻿namespace Simulateur.Dictionnary
{
    public enum EnumCommandList
    {
        REQ_OPEN_FILE = 0x00000001,
        ANS_OPEN_FILE = unchecked((int)0x80000001),
        REQ_WRITE_FILE = 0x00000002,
        ANS_WRITE_FILE = unchecked((int)0x80000002),
        REQ_READ_FILE = 0x00000003,
        ANS_READ_FILE = unchecked((int)0x80000003),
        REQ_CLOSE = 0x00000004,
        ANS_CLOSE = unchecked((int)0x80000004),
        REQ_OPENDIR = 0x00000005,
        ANS_OPENDIR = unchecked((int)0x80000005),
        REQ_READDIR = 0x00000006,
        ANS_READDIR = unchecked((int)0x80000006),
        REQ_CLOSEDIR = 0x00000007,
        ANS_CLOSEDIR = unchecked((int)0x80000007),
        REQ_COMMIT = 0x00000008,
        ANS_COMMIT = unchecked((int)0x80000008),
        REQ_FORMAT = 0x00000009,
        ANS_FORMAT = unchecked((int)0x80000009),

        REQ_CELL_IDENT = 0x00000100,
        ANS_CELL_IDENT = unchecked((int)0x80000100),
        REQ_CELL_CONF = 0x00000101,
        ANS_CELL_CONF = unchecked((int)0x80000101),
        REQ_ACTIVATE_MONITORING = 0x00000102,
        ANS_ACTIVATE_MONITORING = unchecked((int)0x80000102),
        REQ_EXT_STATUS_SUBSCRIBE = 0x00000103,
        ANS_EXT_STATUS_SUBSCRIBE = unchecked((int)0x80000103),
        REQ_EXT_STATUS_UNSUBSCRIBE = 0x00000104,
        ANS_EXT_STATUS_UNSUBSCRIBE = unchecked((int)0x80000104),

        REQ_CHANGE_STATE = 0x00000200,
        ANS_CHANGE_STATE = unchecked((int)0x80000200),
        REQ_LAUNCH = 0x00000201,
        ANS_LAUNCH = unchecked((int)0x80000201),
        REQ_RESET = 0x00000202,
        ANS_RESET = unchecked((int)0x80000202),
        REQ_SET_DATE = 0x00000203,
        ANS_SET_DATE = unchecked((int)0x80000203),
        REQ_GET_DATE = 0x00000204,
        ANS_GET_DATE = unchecked((int)0x80000204),
        REQ_SUBCRIBE = 0x00000205,
        ANS_SUBCRIBE = unchecked((int)0x80000205),
        REQ_UNSUBCRIBE = 0x00000206,
        ANS_UNSUBCRIBE = unchecked((int)0x80000206),
        REQ_TEST = 0x00000207,
        ANS_TEST = unchecked((int)0x80000207),
        REQ_TEST_CLI = 0x00000208,
        ANS_TEST_CLI = unchecked((int)0x80000208),
        REQ_FACTORY_ACCESS = 0x00000209,
        ANS_FACTORY_ACCESS = unchecked((int)0x80000209),
        REQ_SET_ACQUISITION_MODE = 0x0000020A,
        ANS_SET_ACQUISITION_MODE = unchecked((int)0x8000020A),
        REQ_ACQUIRE_TEDS_DATA = 0x0000020B,
        ANS_ACQUIRE_TEDS_DATA = unchecked((int)0x8000020B),
        REQ_STORE_TEDS_DATA = 0x0000020C,
        ANS_STORE_TEDS_DATA = unchecked((int)0x8000020C),
        REQ_VISUAL_IDENTIFICATION = 0x0000020D,
        ANS_VISUAL_IDENTIFICATION = unchecked((int)0x8000020D),

        REQ_STATE = 0x00000300,
        GET_STATE = unchecked((int)0x80000300),

        DISCOVER = 0x00000400,
        ANS_DISCOVER = unchecked((int)0x80000400),

        STREAM_DATA = 0x00000500,
        MCC_KEEPALIVE = 0x00000501,
    }
}
