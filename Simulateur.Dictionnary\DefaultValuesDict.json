{"REQ_OPEN_FILE": ["10-00-00-1C-00-00-FF-FF-FF-FE-00-00-00-00-00-00-80-00-00-01-00-00-00-00-00-00-00-00"], "ANS_OPEN_FILE": [], "REQ_WRITE_FILE": ["10-00-00-24-00-00-FF-FF-FF-FE-00-00-00-00-00-00-80-00-00-02-00-00-00-00-00-00-00-00-00-00-00-00-00-00-00-00"], "ANS_WRITE_FILE": [], "REQ_READ_FILE": [], "ANS_READ_FILE": [], "REQ_CLOSE": ["10-00-00-1C-00-00-FF-FF-FF-FE-00-00-00-00-00-00-80-00-00-04-00-00-00-00-00-00-00-00"], "ANS_CLOSE": [], "REQ_OPENDIR": ["10-00-00-1C-00-00-FF-FF-FF-FE-00-00-00-00-00-00-80-00-00-05-00-00-00-00-00-00-00-00"], "ANS_OPENDIR": [], "REQ_READDIR": ["10-00-00-24-00-00-FF-FF-FF-FE-00-00-00-00-00-00-80-00-00-06-00-00-00-00-00-00-00-00-00-00-00-00-00-00-00-00"], "ANS_READDIR": [], "REQ_CLOSEDIR": ["10-00-00-1C-00-00-FF-FF-FF-FE-00-00-00-00-00-00-80-00-00-07-00-00-00-00-00-00-00-00"], "ANS_CLOSEDIR": [], "REQ_COMMIT": ["10-00-00-18-00-00-FF-FF-FF-FE-00-00-00-00-00-00-80-00-00-08-00-00-00-00"], "ANS_COMMIT": [], "REQ_FORMAT": ["10-00-00-18-00-00-FF-FF-FF-FE-00-00-00-00-00-00-80-00-00-09-00-00-00-00"], "ANS_FORMAT": [], "REQ_CELL_IDENT": ["10-00-00-2C-00-00-FF-FF-FF-FE-00-00-00-00-00-00-80-00-01-00-00-00-00-00-00-00-00-00-00-00-00-00-00-00-00-00-00-00-00-00-00-00-00-00"], "ANS_CELL_IDENT": [], "REQ_CELL_CONF": ["10-00-00-1D-00-00-FF-FF-FF-FE-00-00-00-00-00-00-80-00-01-01-00-00-00-00-00-00-00-00-00"], "ANS_CELL_CONF": [], "REQ_ACTIVATE_MONITORING": ["10-00-00-18-00-00-FF-FF-FF-FE-00-00-00-00-00-00-80-00-01-02-00-00-00-00"], "ANS_ACTIVATE_MONITORING": [], "REQ_EXT_STATUS_SUBSCRIBE": ["10-00-00-18-00-00-FF-FF-FF-FE-00-00-00-00-00-00-80-00-01-03-00-00-00-00"], "ANS_EXT_STATUS_SUBSCRIBE": [], "REQ_EXT_STATUS_UNSUBSCRIBE": ["10-00-00-18-00-00-FF-FF-FF-FE-00-00-00-00-00-00-80-00-01-04-00-00-00-00"], "ANS_EXT_STATUS_UNSUBSCRIBE": [], "REQ_CHANGE_STATE": ["10-00-00-1C-00-00-FF-FF-FF-FE-00-00-00-00-00-00-80-00-02-00-00-00-00-00-00-00-00-00"], "ANS_CHANGE_STATE": [], "REQ_LAUNCH": ["10-00-00-1C-00-00-FF-FF-FF-FE-00-00-00-00-00-00-80-00-02-01-00-00-00-00-00-00-00-00"], "ANS_LAUNCH": [], "REQ_RESET": ["10-00-00-18-00-00-FF-FF-FF-FE-00-00-00-00-00-00-80-00-02-02-00-00-00-00"], "ANS_RESET": [], "REQ_SET_DATE": ["10-00-00-18-00-00-FF-FF-FF-FE-00-00-00-00-00-00-80-00-02-03-00-00-00-00"], "ANS_SET_DATE": [], "REQ_GET_DATE": ["10-00-00-1C-00-00-FF-FF-FF-FE-00-00-00-00-00-00-80-00-02-04-00-00-00-00-00-00-00-00"], "ANS_GET_DATE": [], "REQ_SUBCRIBE": ["10-00-00-18-00-00-FF-FF-FF-FE-00-00-00-00-00-00-80-00-02-05-00-00-00-00"], "ANS_SUBCRIBE": [], "REQ_UNSUBCRIBE": ["10-00-00-18-00-00-FF-FF-FF-FE-00-00-00-00-00-00-80-00-02-06-00-00-00-00"], "ANS_UNSUBCRIBE": [], "REQ_TEST": ["10-00-00-18-00-00-FF-FF-FF-FE-00-00-00-00-00-00-80-00-02-07-00-00-00-00"], "ANS_TEST": [], "REQ_TEST_CLI": ["10-00-00-18-00-00-FF-FF-FF-FE-00-00-00-00-00-00-80-00-02-08-00-00-00-00"], "ANS_TEST_CLI": [], "REQ_FACTORY_ACCESS": ["10-00-00-18-00-00-FF-FF-FF-FE-00-00-00-00-00-00-80-00-02-09-00-00-00-00"], "ANS_FACTORY_ACCESS": [], "REQ_SET_ACQUISITION_MODE": ["10-00-00-18-00-00-FF-FF-FF-FE-00-00-00-00-00-00-80-00-02-0A-00-00-00-00"], "ANS_SET_ACQUISITION_MODE": [], "REQ_ACQUIRE_TEDS_DATA": [], "ANS_ACQUIRE_TEDS_DATA": [], "REQ_STORE_TEDS_DATA": [], "ANS_STORE_TEDS_DATA": [], "REQ_VISUAL_IDENTIFICATION": [], "ANS_VISUAL_IDENTIFICATION": [], "REQ_STATE": ["10-00-00-24-00-00-00-00-00-00-00-00-00-00-00-00-00-00-00-00-00-00-00-00-00-00-00-00-00-00-00-00-00-00-00-00"], "GET_STATE": [], "DISCOVER": ["10-00-00-22-00-00-FF-FF-FF-FE-00-00-00-00-00-00-80-00-04-00-00-00-00-00-00-00-00-00-00-00-00-00-00-00"], "ANS_DISCOVER": [], "STREAM_DATA": [], "MCC_KEEPALIVE": ["10-00-00-14-00-00-FF-FF-FF-FE-00-00-00-00-00-00-00-00-05-01"]}