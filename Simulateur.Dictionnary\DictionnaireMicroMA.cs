﻿using System;
using System.Collections.Generic;
using System.Diagnostics.Metrics;
using System.Text.RegularExpressions;
using Newtonsoft.Json;

namespace Simulateur.Dictionnary
{
    /// <summary>
    /// La classe DictionnaireMicroMA<T> agit comme un dictionnaire typé pour stocker et fournir des réponses
    /// cycliques à des messages, selon leur type (clé = nom de la commande).
    /// </summary>
    /// <typeparam name="T">Type des réponses stockées (string, byte[], etc.)</typeparam>
    public class DictionnaireMicroMA<T>
    {
        private static readonly Regex HexDashPattern = new Regex(@"^(?:[0-9A-F]{2})(?:-[0-9A-F]{2})*$", RegexOptions.Compiled);

        private static DictionnaireMicroMA<T>? _instance;
        private static readonly object _lock = new object();

        // Dictionnaire principal : associe chaque type de commande à un objet CountedResponse
        private Dictionary<string, CountedResponse<T>> _responses;

        /// <summary>
        /// Accès singleton à l'instance unique du dictionnaire
        /// </summary>
        public static DictionnaireMicroMA<T> Instance
        {
            get
            {
                if (_instance == null)
                {
                    lock (_lock)
                    {
                        if (_instance == null)
                            _instance = new DictionnaireMicroMA<T>();
                    }
                }
                return _instance;
            }
        }

        // Constructeur privé, initialise un CountedResponse vide pour chaque type de commande connu
        private DictionnaireMicroMA()
        {
            try
            {
                LoadDefaults();
            }
            catch (Exception e)
            {
                ResetEmpty();
                Console.WriteLine(e);
                //throw;
            }
        }

        private static byte[] ParseHexToString(string s)
        {
            if (string.IsNullOrWhiteSpace(s))
                throw new FormatException("Chaîne du dictionnaire vide (pour une réponse en hexa).");

            s = s.Trim();

            // Regex: "HH(-HH)*":
            //chaque taille d'octet == 2;
            //séparé UNQIUEMENT par un '-';
            //UNIQUE caractère accepté [0-9A-F]
            if (!HexDashPattern.IsMatch(s))
                throw new FormatException($"Format hexa invalide '{s}'. Attendu: 'HH(-HH)*', séparés uniquement par '-'.");

            var parts = s.Split('-');
            var bytes = new byte[parts.Length];
            for (int i = 0; i < parts.Length; i++)
                bytes[i] = Convert.ToByte(parts[i], 16);

            return bytes;
        }

        private void LoadDefaults()
        {
            _responses = new Dictionary<string, CountedResponse<T>>();

            foreach (EnumCommandList type in Enum.GetValues(typeof(EnumCommandList)))
            {
                _responses[type.ToString()] = new CountedResponse<T>();
            }

            string path = Path.Combine(AppContext.BaseDirectory, "../../../../Simulateur.Dictionnary/DefaultValuesDict.json");
            if (!File.Exists(path)) return;

            string json = File.ReadAllText(path);

            if (typeof(T) == typeof(byte[]))
            {
                var data = JsonConvert.DeserializeObject<Dictionary<string, List<string>>>(json);
                if (data == null) return;

                foreach (var kv in data)
                {
                    var listBytes = new List<byte[]>();

                    if (kv.Value != null)
                    {
                        foreach (var hex in kv.Value)
                        {
                            listBytes.Add(ParseHexToString(hex));
                        }
                    }

                    var typedList = listBytes.Select(b => (T)(object)b).ToList();
                    _responses[kv.Key].SetResponses(typedList);
                }
            }
            else
            {
                //use case, si on entre autre chose que des byte[] sous format string dans le JSON (pas sensé être le cas)
                var data = JsonConvert.DeserializeObject<Dictionary<string, List<T>>>(json);
                if (data == null) return;

                foreach (var kv in data)
                {
                    _responses[kv.Key].SetResponses(kv.Value ?? new List<T>());
                }
            }

        }

        public void ResetDefault()
        {
            LoadDefaults();
        }

        /// <summary>
        /// Définit ou redéfinit les réponses pour une commande donnée (clé).
        /// </summary>
        /// <param name="key">Nom de la commande (clé)</param>
        /// <param name="InputResponses">Liste des réponses à renvoyer cycliquement</param>
        public void SetResponses(string key, List<T> InputResponses)
        {
            if (_responses.ContainsKey(key))
            {
                _responses[key].SetResponses(InputResponses);
            }
            else
            {
                throw new KeyNotFoundException($"{key} is not a defined command.");
            }
        }

        /// <summary>
        /// Récupère la prochaine réponse pour une commande donnée, en mode cyclique.
        /// </summary>
        /// <param name="key">Nom de la commande</param>
        /// <returns>Réponse du type T</returns>
        public T GetResponse(string key)
        {
            if (_responses.TryGetValue(key, out CountedResponse<T> obj))
            {
                return obj.GetNextResponses();
            }
            return default(T);
        }

        /// <summary>
        /// Réinitialise toutes les réponses (utile pour les tests).
        /// </summary>
        public void ResetEmpty()
        {
            foreach (var item in _responses.Values)
            {
                item.SetResponses(new List<T>());
            }
        }
    }
}