<Window x:Class="IHM_Simulateur.View.MainWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        xmlns:vm="clr-namespace:IHM_Simulateur.ViewModel"
        mc:Ignorable="d"
        d:DataContext="{d:DesignInstance vm:MainViewModel}"
        Title="Simulateur" Height="500" Width="800"
        Icon="../Ressources/Images/logo.ico">

    <!-- Interface principale du simulateur : permet d'afficher les logs et de contrôler les switches -->
    <Grid Margin="20">
        <!-- 3 lignes et 2 colonnes principales -->
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>
        <Grid.ColumnDefinitions>
            <ColumnDefinition Width="2*"/>
            <ColumnDefinition Width="1*"/>
        </Grid.ColumnDefinitions>

        <!-- Titre de la fenêtre -->
        <StackPanel Grid.Row="0" Grid.ColumnSpan="2" Orientation="Horizontal" HorizontalAlignment="Center" Margin="0,0,0,20">
            <TextBlock Text="WIP-IHM Simulateur AIM" FontSize="24" FontWeight="Bold" VerticalAlignment="Center"/>
        </StackPanel>

        <!-- Zone d'affichage des logs (lecture seule) -->
        <ScrollViewer Grid.Row="1" Grid.Column="0" Height="300">
            <TextBox x:Name="OutputTextBox"
                     Text="{Binding JoinedLogEntries, Mode=OneWay}"
                     TextWrapping="Wrap"
                     AcceptsReturn="True"
                     VerticalScrollBarVisibility="Auto"
                     IsReadOnly="True" FontFamily="Consolas" FontSize="14" Padding="10" />
        </ScrollViewer>

        <!-- Zone de contrôle avec les switches et le bouton options -->
        <Grid Grid.Row="1" Grid.Column="1" Margin="20,0">
            <Grid.RowDefinitions>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="Auto"/>
            </Grid.RowDefinitions>

            <!-- Switch 1 -->
            <StackPanel Orientation="Horizontal" Grid.Row="0" Margin="0,10">
                <ToggleButton x:Name="Switch1" Width="50" Height="30"
                              Command="{Binding ToggleSwitchCommand}" CommandParameter="Switch1"
                              Style="{StaticResource StyleSwitch}"/>
                <TextBlock Text="Option 1" FontSize="16" VerticalAlignment="Center" Margin="10,0,0,0"/>
            </StackPanel>

            <!-- Switch 2 -->
            <StackPanel Orientation="Horizontal" Grid.Row="1" Margin="0,10" VerticalAlignment="Center">
                <TextBlock Text="Délai :" FontSize="16" VerticalAlignment="Center" Margin="0,0,10,0"/>

                <Slider x:Name="DelaySlider"
                    Minimum="1" Maximum="60"
                    Value="{Binding DelayValue, Mode=TwoWay}"
                    Width="200" TickFrequency="1"
                    IsSnapToTickEnabled="True"
                    SmallChange="1" LargeChange="5"/>

                <TextBlock Text="{Binding DelayValue}"
               FontSize="16" VerticalAlignment="Center" Margin="10,0,0,0"/>
            </StackPanel>

            <!-- Ouvre la fenêtre des options avancées -->
            <StackPanel Orientation="Horizontal" Grid.Row="3" Margin="0,10">
                <Button Content="Options"
                        Width="100" Height="30"
                        Command="{Binding OpenOptionsCommand}" />
            </StackPanel>

            <!-- Zone de texte pour setup les retours des messages -->
            <StackPanel Grid.Row="4" Margin="0,10" Orientation="Vertical">
                <TextBox
                    Text="{Binding KeyValueInputRaw, UpdateSourceTrigger=PropertyChanged}"
                    TextWrapping="Wrap"
                    AcceptsReturn="True"
                    VerticalScrollBarVisibility="Auto"
                    FontFamily="Consolas" FontSize="12" Height="100"
                    ToolTip="Syntaxe : REQ_CELL_IDENT => { &quot;val1&quot;, &quot;val2&quot; }"
                    />
                <Button Content="Appliquer" Width="100" Margin="0,10,0,0"
                Command="{Binding ApplyKeyValueCommand}" />
            </StackPanel>

        </Grid>

        <!-- Bouton START visible uniquement quand le simulateur est inactif -->
        <ToggleButton x:Name="StartButton"
                      Grid.Row="2" Grid.Column="3" Content="START" FontSize="18" FontWeight="Bold"
                      Width="120" Height="50"
                      Command="{Binding ToggleSimulationCommand}">
            <ToggleButton.Style>
                <Style TargetType="ToggleButton">
                    <Setter Property="Visibility" Value="Visible"/>
                    <Style.Triggers>
                        <DataTrigger Binding="{Binding IsSimulatorActivated}" Value="True">
                            <Setter Property="Visibility" Value="Collapsed"/>
                        </DataTrigger>
                    </Style.Triggers>
                </Style>
            </ToggleButton.Style>
        </ToggleButton>

        <!-- Bouton STOP visible uniquement quand le simulateur est actif -->
        <ToggleButton x:Name="StopButton"
                      Grid.Row="2" Grid.Column="3" Content="STOP" FontSize="18" FontWeight="Bold"
                      Width="120" Height="50"
                      Command="{Binding ToggleSimulationCommand}">
            <ToggleButton.Style>
                <Style TargetType="ToggleButton">
                    <Setter Property="Visibility" Value="Collapsed"/>
                    <Style.Triggers>
                        <DataTrigger Binding="{Binding IsSimulatorActivated}" Value="True">
                            <Setter Property="Visibility" Value="Visible"/>
                        </DataTrigger>
                    </Style.Triggers>
                </Style>
            </ToggleButton.Style>
        </ToggleButton>
    </Grid>
</Window>
