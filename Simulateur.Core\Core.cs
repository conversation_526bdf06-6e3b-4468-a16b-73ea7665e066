﻿using System.Net;
using NLog;
using MicroMa.MicroMaProtocol.Messages;
using Simulateur.Commons;
using Simulateur.Communication;
using NLog.Targets;
using Simulateur.GlobalConf;

using ResponseDico = Simulateur.Dictionnary.DictionnaireMicroMA<byte[]>;

namespace Simulateur.Core
{
    public class Core : ICoreMicroMA
    {
        public static Action<string>? OnMessageReceived { get; set; }

        private readonly ConfigSimu param;

        private IUDPEthernet? serviceUDP;
        private int OptionalPort;
        private static Core? _instance;
        private static readonly object _lock = new object();

        private static readonly Logger logger = LogManager.GetCurrentClassLogger();

        private int WatchDogDelay;
        private bool IsWatchdogActivated;

        /* (Singleton pattern) */
        // Propriété pour accéder à l'instance unique
        public static Core Instance
        {
            get
            {
                if (_instance == null)
                {
                    lock (_lock)
                    {
                        if (_instance == null)
                        {
                            _instance = new Core();
                        }
                    }
                }
                return _instance;
            }
        }

        // Constructeur privé pour empêcher l'instanciation externe
        private Core()
        {
            param = ConfigSimu.Instance;
        }

        public void Start()
        {
            HistoLog.Log(logger, "Application démarrée", LogLevel.Info);
            switch (param.ModuleSimulated)
            {
                case "MicroMA":
                    serviceUDP = new UDPEthernet(OptionalPort, WatchDogDelay, IsWatchdogActivated);

                    serviceUDP.OnMessageReceived = async (msg, sender) =>
                    {
                        HistoLog.Log(logger, $"Message reçu :{BitConverter.ToString(msg)}", LogLevel.Info);
                        int delayMs = param.Delay * 1000;
                        if (delayMs > 0)
                            await Task.Delay(delayMs);

                        var response = Processing(msg);
                        HistoLog.Log(logger, $"{sender} : {BitConverter.ToString(response)}", LogLevel.Info);
                        await SendMessage(response, sender);
                    };

                    Task t = serviceUDP.Start();
                    break;
                case "XMA":
                    break;
                case "MDR":
                    break;
                default:
                    break;
            }
        }

        public void InitResponses(string message, List<byte[]> responses)
        {
            if (string.IsNullOrEmpty(message))
            {
                HistoLog.Log(logger, "Message vide fourni à InitResponses", LogLevel.Warn);
                return;
            }

            if (responses == null)
            {
                HistoLog.Log(logger, "Liste de réponses null fournie à InitResponses", LogLevel.Warn);
                return;
            }

            try
            {
                var dicoResponse = ResponseDico.Instance;
                dicoResponse.SetResponses(message, responses);
                HistoLog.Log(logger, $"Réponses initialisées pour {message} : {responses.Count} éléments", LogLevel.Debug);
            }
            catch (Exception ex)
            {
                HistoLog.Log(logger, $"Erreur lors de l'initialisation des réponses pour {message}: {ex.Message}", LogLevel.Error);
            }
        }

        private byte[] Processing(byte[] message)
        {
            try
            {
                if (message == null || message.Length == 0)
                {
                    HistoLog.Log(logger, "Message vide reçu pour processing", LogLevel.Warn);
                    return Array.Empty<byte>();
                }

                var parsedMessage = MessageFactory.GetMessage(message);
                if (parsedMessage?.Header?.Idc == null)
                {
                    HistoLog.Log(logger, "Message invalide : Header ou Idc manquant", LogLevel.Warn);
                    return Array.Empty<byte>();
                }

                string typeMessage = parsedMessage.Header.Idc.ToString();
                return ResponseDico.Instance.GetResponse(typeMessage) ?? Array.Empty<byte>();
            }
            catch (Exception ex)
            {
                HistoLog.Log(logger, $"Erreur lors du processing du message: {ex.Message}", LogLevel.Error);
                return Array.Empty<byte>();
            }
        }

        internal async Task SendMessage(byte[] message, IPEndPoint sender)
        {
            if (message == null || message.Length == 0)
            {
                HistoLog.Log(logger, "Aucune réponse disponible lors de l'envoie.", LogLevel.Warn);
                return;
            }

            if (sender == null)
            {
                HistoLog.Log(logger, "Endpoint de destination null", LogLevel.Error);
                return;
            }

            try
            {
                //Send message to IHM
                OnMessageReceived?.Invoke(BitConverter.ToString(message));
                //Send message to client
                if (serviceUDP != null)
                {
                    await serviceUDP.SendMessage(message, sender);
                }
                else
                {
                    HistoLog.Log(logger, "Service UDP non initialisé", LogLevel.Error);
                }
            }
            catch (Exception ex)
            {
                HistoLog.Log(logger, $"Erreur lors de l'envoi du message: {ex.Message}", LogLevel.Error);
            }
        }

        public void Stop()
        {
            serviceUDP?.Stop();
            HistoLog.Log(logger, "Application fermée", LogLevel.Info);
        }

        public void SetLogFileName(string path)
        {
            var config = LogManager.Configuration;
            var fileTarget = config.FindTargetByName<FileTarget>("file");

            if (fileTarget != null)
            {
                fileTarget.FileName = $"{path}/logs/log_${{date:format=yyyyMMdd}}.txt";
                LogManager.ReconfigExistingLoggers();
            }
        }

        public void UpdatePort(int port)
        {
            OptionalPort = port;
        }

        public void ActivateWatchDog(int watchDogDelay)
        {
            IsWatchdogActivated = true;
            if (watchDogDelay > 0)
            {
                this.WatchDogDelay = watchDogDelay;
            }
        }

        /*OptionsViewModel opts = new ViewModel.OptionsViewModel();
        opts.ActivateWatchDog = async(delay) =>
        {
            ActivateWatchDog(delay);
        };*/
    }
}
