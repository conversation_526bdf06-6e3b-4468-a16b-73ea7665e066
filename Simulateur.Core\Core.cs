﻿using System.Net;
using NLog;
using MicroMa.MicroMaProtocol.Messages;
using Simulateur.Commons;
using Simulateur.Communication;
using NLog.Targets;
using Simulateur.GlobalConf;

using ResponseDico = Simulateur.Dictionnary.DictionnaireMicroMA<byte[]>;

namespace Simulateur.Core
{
    public class Core : ICoreMicroMA
    {
        public static Action<string> OnMessageReceived { get; set; }

        private ConfigSimu param;

        private IUDPEthernet serviceUDP;
        private int OptionalPort;
        private static Core? _instance;
        private static readonly object _lock = new object();

        private static Logger logger = LogManager.GetCurrentClassLogger();

        private int WatchDogDelay;
        private bool IsWatchdogActivated;

        /* (Singleton pattern) */
        // Propriété pour accéder à l'instance unique
        public static Core Instance
        {
            get
            {
                if (_instance == null)
                {
                    lock (_lock)
                    {
                        if (_instance == null)
                        {
                            _instance = new Core();
                        }
                    }
                }
                return _instance;
            }
        }

        // Constructeur privé pour empêcher l'instanciation externe
        private Core()
        {
            param = ConfigSimu.Instance;
        }

        public void Start()
        {
            HistoLog.Log(logger, "Application démarrée", LogLevel.Info);
            switch (param.ModuleSimulated)
            {
                case "MicroMA":
                    serviceUDP = new UDPEthernet(OptionalPort, WatchDogDelay, IsWatchdogActivated);

                    serviceUDP.OnMessageReceived = async (msg, sender) =>
                    {
                        HistoLog.Log(logger, $"Message reçu :{BitConverter.ToString(msg)}", LogLevel.Info);
                        int delayMs = param.Delay * 1000;
                        if (delayMs > 0)
                            await Task.Delay(delayMs);

                        var response = Processing(msg);
                        HistoLog.Log(logger, $"{sender} : {BitConverter.ToString(response)}", LogLevel.Info);
                        await SendMessage(response, sender);
                    };

                    Task t = serviceUDP.Start();
                    break;
                case "XMA":
                    break;
                case "MDR":
                    break;
                default:
                    break;
            }
        }

        public void InitResponses(string message, List<byte[]> responses)
        {
            var dicoResponse = ResponseDico.Instance;
            dicoResponse.SetResponses(message, responses);
        }

        private byte[] Processing(byte[] message)
        {
            string typeMessage = MessageFactory.GetMessage(message).Header.Idc.ToString();

            return ResponseDico.Instance.GetResponse(typeMessage) ?? Array.Empty<byte>();
        }

        internal async Task SendMessage(byte[] message, IPEndPoint sender)
        {
            if (message == null || message.Length == 0)
            {
                HistoLog.Log(logger, "Aucune réponse disponible lors de l'envoie.", LogLevel.Warn);
                return;
            }
            //Send message to IHM
            OnMessageReceived?.Invoke(BitConverter.ToString(message));
            //Send message to client
            await serviceUDP.SendMessage(message, sender);
        }

        public void Stop()
        {
            serviceUDP.Stop();
            HistoLog.Log(logger, "Application fermée", LogLevel.Info);
        }

        public void SetLogFileName(string path)
        {
            var config = LogManager.Configuration;
            var fileTarget = config.FindTargetByName<FileTarget>("file");

            if (fileTarget != null)
            {
                fileTarget.FileName = $"{path}/logs/log_${{date:format=yyyyMMdd}}.txt";
                LogManager.ReconfigExistingLoggers();
            }
        }

        public void UpdatePort(int port)
        {
            OptionalPort = port;
        }

        public void ActivateWatchDog(int WatchDogDelay)
        {
            IsWatchdogActivated = true;
            if (WatchDogDelay != null && WatchDogDelay > 0)
            {
                this.WatchDogDelay = WatchDogDelay;
            }
        }

        /*OptionsViewModel opts = new ViewModel.OptionsViewModel();
        opts.ActivateWatchDog = async(delay) =>
        {
            ActivateWatchDog(delay);
        };*/
    }
}
