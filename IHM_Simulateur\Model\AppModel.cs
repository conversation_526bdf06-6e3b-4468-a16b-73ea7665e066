﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using IHM_Simulateur.Interfaces;

namespace IHM_Simulateur.Model
{
    public class AppModel : IAppModel
    {
        public string LogText { get; set; } = "Bienvenue dans l'application !\n";
        public bool Switch1 { get; set; }
        public bool Switch2 { get; set; }
        public bool Switch3 { get; set; }
        public bool Switch4 { get; set; }
        public bool IsSimulatorActivated { get; set; }
    }
}
