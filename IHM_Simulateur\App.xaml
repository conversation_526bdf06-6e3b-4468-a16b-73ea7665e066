﻿<Application x:Class="IHM_Simulateur.App"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:local="clr-namespace:IHM_Simulateur"
             StartupUri="/View/MainWindow.xaml">
    <Application.Resources>
        <!-- Style Switch for ToggleButton -->
        <Style x:Key="StyleSwitch" TargetType="ToggleButton">
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="ToggleButton">
                        <Grid Width="50" Height="30">
                            <Border x:Name="BackgroundRect" Width="50" Height="30" CornerRadius="15"
                            Background="Gray"/>
                            <Ellipse x:Name="Knob" Width="24" Height="24"
                             Fill="White"
                             HorizontalAlignment="Left"
                             Margin="3,3,0,3"/>
                        </Grid>
                        <ControlTemplate.Triggers>
                            <Trigger Property="IsChecked" Value="True">
                                <Setter TargetName="BackgroundRect" Property="Background" Value="Green"/>
                                <Setter TargetName="Knob" Property="HorizontalAlignment" Value="Right"/>
                            </Trigger>
                        </ControlTemplate.Triggers>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
            <Setter Property="Background" Value="Transparent"/>
            <Setter Property="BorderThickness" Value="0"/>
        </Style>
    </Application.Resources>
</Application>
