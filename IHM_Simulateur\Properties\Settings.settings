﻿<?xml version='1.0' encoding='utf-8'?>
<SettingsFile xmlns="http://schemas.microsoft.com/VisualStudio/2004/01/settings" CurrentProfile="(Default)" GeneratedClassNamespace="IHM_Simulateur.Properties" GeneratedClassName="Settings">
  <Profiles />
  <Settings>
    <Setting Name="Delay" Type="System.Int32" Scope="Application">
      <Value Profile="(Default)">1</Value>
    </Setting>
    <Setting Name="Debug" Type="System.Boolean" Scope="Application">
      <Value Profile="(Default)">False</Value>
    </Setting>
    <Setting Name="ModuleSimulated" Type="System.String" Scope="Application">
      <Value Profile="(Default)">MicroMA</Value>
    </Setting>
    <Setting Name="CommunicationService" Type="System.String" Scope="Application">
      <Value Profile="(Default)">Ethernet</Value>
    </Setting>
    <Setting Name="IsWatchdogActivated" Type="System.Boolean" Scope="Application">
      <Value Profile="(Default)">True</Value>
    </Setting>
    <Setting Name="WatchDogDelay" Type="System.Int32" Scope="Application">
      <Value Profile="(Default)">1</Value>
    </Setting>
    <Setting Name="LogFilePath" Type="System.String" Scope="Application">
      <Value Profile="(Default)">${basedir}</Value>
    </Setting>
  </Settings>
</SettingsFile>