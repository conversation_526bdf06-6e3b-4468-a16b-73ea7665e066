﻿namespace Simulateur.Dictionnary
{
    /// <summary>
    /// La classe <c>CountedResponse</c> est un object, contenant la liste des réponses, ainsi que le nombre d'appel fait à celle-ci.
    /// Elle permet de récupérer la réponse à l'index du nombre d'appel fait à celle-ci, recommence à zéro si le nombre d'appel est supérieur aux nombres d'éléments de la liste.
    /// </summary>
    /// <typeparam name="T">Type Template pour le type de réponse du message</typeparam>
    internal class CountedResponse<T>
    {
        private int CountCall;
        private List<T> Responses;

        public CountedResponse()
        {
            CountCall = 0;
            Responses = new List<T>();
        }

        public void SetResponses(List<T> InputResponses)
        {
            CountCall = 0;
            Responses = InputResponses;
        }

        public T? GetNextResponses()
        {
            int length = Responses.Count;
            if (length == 0)
                return default(T);

            T otp = Responses[CountCall];
            CountCall = (CountCall + 1) % length;
            return otp;
        }
    }
}
