﻿using System;
using System.Configuration;

namespace Simulateur.GlobalConf
{
    public class ConfigSimu
    {
        /* Attributs */
        // Switch case in core
        public string ModuleSimulated { get; set; }
        // To simulate Packet Lost
        public int Delay { get; set; }
        public bool Debug { get; set; }
        //public string CommunicationService { get; set; }
        //public bool IsWatchdogActivated { get; set; }
        //public int WatchDogDelay { get; set; }
        public string LogFilePath { get; set; }


        /* (Singleton pattern) */
        private static ConfigSimu? _instance;
        private static readonly object _lock = new object();

        // Unique Instance acess
        public static ConfigSimu Instance
        {
            get
            {
                if (_instance == null)
                {
                    lock (_lock)
                    {
                        if (_instance == null)
                        {
                            _instance = new ConfigSimu();
                        }
                    }
                }
                return _instance;
            }
        }

        private ConfigSimu()
        {
            //load settings from App.config, if not defined set to default value
            ModuleSimulated = ConfigurationManager.AppSettings["ModuleSimulated"] ?? "MicroMA";
            Delay = int.Parse(ConfigurationManager.AppSettings["Delay"] ?? "1");
            Debug = bool.Parse(ConfigurationManager.AppSettings["Debug"] ?? "false");
            //CommunicationPort = int.Parse(ConfigurationManager.AppSettings["CommunicationPort"] ?? "5000");
            //CommunicationService = ConfigurationManager.AppSettings["CommunicationService"] ?? "UDP"; -- Déplacer au niveau du Core
            //IsWatchdogActivated = bool.Parse(ConfigurationManager.AppSettings["IsWatchdogActivated"] ?? "true");
            //WatchDogDelay = int.Parse(ConfigurationManager.AppSettings["WatchDogDelay"] ?? "1");
            LogFilePath = ConfigurationManager.AppSettings["LogFilePath"] ?? "${basedir}";

            //plutot une méthode pour desactiver le watchdog, pas dans la conf
        }
    }
}
