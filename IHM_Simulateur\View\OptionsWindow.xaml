﻿<Window x:Class="IHM_Simulateur.View.OptionsWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        xmlns:vm="clr-namespace:IHM_Simulateur.ViewModel"
        xmlns:local="clr-namespace:IHM_Simulateur"
        mc:Ignorable="d"
        d:DataContext="{d:DesignInstance vm:OptionsViewModel}"
        Title="Options" Height="450" Width="400"
        WindowStartupLocation="CenterOwner">

    <!-- Fenêtre de configuration avancée du simulateur -->
    <StackPanel Margin="20" VerticalAlignment="Center">

        <!-- Choix du module simulé -->
        <TextBlock Text="Nom du module" />
        <ComboBox ItemsSource="{Binding ListModules}"
                  SelectedItem="{Binding ModuleName}" Margin="0,5"/>

        <!-- Délai de réponse -->
        <TextBlock Text="Délai" />
        <TextBox Text="{Binding Delay, ValidatesOnDataErrors=True, UpdateSourceTrigger=PropertyChanged}" Margin="0,5"/>

        <!-- Chemin d'accès pour les fichiers logs -->
        <TextBlock Text="Chemin d'accès pour les fichiers logs" />
        <TextBox Text="{Binding LogFilePath, ValidatesOnDataErrors=True, UpdateSourceTrigger=PropertyChanged}" Margin="0,5"/>

        <!-- Case à cocher pour activer le mode debug -->
        <CheckBox Content="Activer debug" IsChecked="{Binding Debug}" Margin="0,10"/>

        <!-- Case à cocher pour activer ou désactiver le watchdog -->
        <CheckBox Content="Activer watchdog" IsChecked="{Binding IsWatchdogActivated}" Margin="0,5"/>

        <!-- Délai du watchdog -->
        <TextBlock Text="Délai du watchdog" />
        <TextBox Text="{Binding WatchDogDelay, ValidatesOnDataErrors=True, UpdateSourceTrigger=PropertyChanged}" Margin="0,5"/>

        <!-- Bouton de validation -->
        <Button Content="Valider" Margin="0,20,0,0" Width="100" HorizontalAlignment="Center"
                Command="{Binding ApplyCommand}"/>
    </StackPanel>
</Window>
