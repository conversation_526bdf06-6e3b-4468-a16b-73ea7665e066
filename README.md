# README - Simulateur 🚀
Ce projet a pour but de simuler (et non émuler) les modules XMA, µMA et MDR. Des fichiers .dll et .exe seront générés.

<!--build-passing-brightgreen
any_text-you_like-blue
-->
![Build Status](https://img.shields.io/badge/build-passing-brightgreen)
![Version](https://img.shields.io/badge/version-1.0.0-blue)
![.NET](https://img.shields.io/badge/.NET-9.0-darkblue)

## 📖 Table des Matières
- [À propos](#-À-propos)
- [Installation](#-Installation)
- [Configuration](#-Configuration)
- [Utilisation](#-Utilisation)
- [Roadmap](#-Roadmap)
- [Remerciements](#-Remerciements)

## 🧐 À propos
Ce projet est une application qui permet de simuler des modules produit par SDS. Il utilise le framework .NET Core 9 pour fournir une solution de connection en Ethernet (d'autre à venir).   
Ce projet contient plusieurs parties : 
- IHM en WPF
- Librairies µMA (.dll) :
    - Core (contentant les comportements à simuler)
    - Communication (servant à établir les échanges ethernet)
    - Dictionnaires (stockage des valeurs de retour suivant le type de message)
    - Commons (librairies de modules communs)
 

## 🛠️ Installation

### Prérequis
Avant de commencer, assurez-vous d'avoir installé les éléments suivants :
- [Viual Studio](https://visualstudio.microsoft.com/fr) version 2022
- [.NET](https://dotnet.microsoft.com/fr-fr/download/dotnet/9.0)
- Le NuGet propre au protocole de MicroMA `MicroMa.MicroMaProtocol.1.0.2.nupkg`

### Étapes d'installation
1. **Cloner le projet**  

- Depuis TortoiseHG, cloner le dépôt distant depuis le lien `http://vm-hg-ulis.in-snec.com/hg/EZ/dau_simu`
2. Ajouter le dossier contenant le nugget des protocole au projet.
1. **Compiler depuis Visual Studio**  
1. **Lancer le .exe** se trouvant `\dau_simu\IHM_Simulateur\bin\Debug\net9.0-windows\IHM_Simulateur.exe` 

## ⚙️ Configuration
La configuration des comportements du Simulareur se retrouvent dans le fichier `dau_simu\IHM_Simulateur\App.config`.  
On y retrouve notamment :
- **Port de communication** : Défini par un entier, le port à ouvrir pour une communication Ethernet.
- **Module simulé** : Défini par une chaîne de caractères parmis `{"MicroMA", "XMA", "MDR"}`.
- **Delai** : Défini par un entier, représentant le temps de réponse entre requête-réponse, par défaut à 1s.
- **Mode debug** : Défini par un booléan, active la verbose. Affiche les détails entre les échanges.
- **Mode de communication** : Défini par une chaîne de caractères, dépendemment du module plusieurs mode de communication peuvent être simulé.  
Ex : `{"Ethernet"}`


## 🚀 Utilisation
### Structure de l'IHM
L'IHM est en WIP, elle se présente avec un boutton 'Start' qui lance un serveur UDP sur l'IP `127.0.0.1:5000`.  
Actuellement le boutton 1 permet de modifié le port de `5000` à `5001`.  
Le boutton 2 permet de modifié le delai avant la réponse de `1s` à `3s`.  
La fenêtre centrale de contenu de texte, permet de visualiser les échanges de message.  
Un dossier **logs** est retrouvable dans le chemin `dau_simu\IHM_Simulateur\bin\Debug\net9.0-windows\logs` contenant les fichiers log journalier répertoriant l'ensemble des commandes et échanges du simulateur.  

### Les méthodes exposées du Core sont :  
- `void Start(Dictionary<string, string> parameters)` : Permettant la mise en route du Core avec le comportements suivant les parameters.  
- `void InitResponses(string message, List<string> responses)` : Permettant de définir une liste de réponse d'un type de message spécifiquement.  

**Exemple d'utilisation** : 
```C#
InitResponses("REQ_OPEN_FILE", new List<string> { "First response", "Second response" });
InitResponses("REQ_CELL_IDENT", new List<byte[]> { [0,1,0,0,0,1,1,0], [0,1,0,1,0,0,1,1] });
```  
- `void Stop()` : Permettant la fermeture du Core et de ses éléments.  

## 🛤️ Roadmap
- [x] Mise en place du projet
- [x] Instance UDP, crée la connection
- [x] Interface graphique
- [x] Respect des normes MVVM
- [x] Récupérer l'énumération des types de IMessages (MicroMA)
- [x] Mise en place d'un dictionnaire [clé]->object, pour stocker et compter les réponses
- [x] Récupérer le NuGet du MicroMA.protocole
- [x] Script test qui envoie un message
- [x] Gestion d'une configuration
- [x] Fichier logs
- [ ] Test Unitaire


## 🎉 Remerciements
Auteurs :
- [FACCHIN Maxime](.)
- [VALENTE Gabriel](.)
