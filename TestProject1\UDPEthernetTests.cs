﻿using Microsoft.VisualStudio.TestTools.UnitTesting;
using Simulateur.Communication;
using System;
using System.Net;
using System.Net.Sockets;
using System.Text;
using System.Threading;
using System.Threading.Tasks;

namespace Simu.Tests
{
    [TestClass]
    public class UDPEthernetTests
    {
        private int GetFreePort()
        {
            using (var udpClient = new UdpClient(0)) // bind sur un port libre temporairement
            {
                return ((IPEndPoint)udpClient.Client.LocalEndPoint).Port;
            }
        }

        [TestMethod]
        public void Start_ShouldOpenUdpClient()
        {
            int port = GetFreePort();
            var udp = new UDPEthernet(port);
            var _ = udp.Start();
            Thread.Sleep(100); // laisser le temps au listener de démarrer
            Assert.IsNotNull(udp.CurrentUdpClient);
            udp.Stop();
        }

        [TestMethod]
        public void SendMessage_ShouldNotThrow_WhenUdpClientIsNull()
        {
            int port = GetFreePort();
            var udp = new UDPEthernet(port);
            var endpoint = new IPEndPoint(IPAddress.Loopback, 5000);
            udp.SendMessage("test", endpoint); // ne doit pas lever d'exception
        }


        [TestMethod]
        public async Task WatchDog_ShouldInvokeAction()
        {
            bool actionCalled = false;
            using var cts = new CancellationTokenSource();
            var watchdog = new WatchDog(1, () => actionCalled = true, cts.Token);

            var task = watchdog.IsAlive();
            await Task.Delay(1100);
            cts.Cancel();

            Assert.IsTrue(actionCalled);
        }

        [TestMethod]
        public async Task WatchDog_ShouldRespectCancellation()
        {
            bool actionCalled = false;
            using var cts = new CancellationTokenSource();
            var watchdog = new WatchDog(5, () => actionCalled = true, cts.Token);

            var task = watchdog.IsAlive();
            cts.Cancel();
            await Task.Delay(1000);

            Assert.IsFalse(actionCalled);
        }
    }
}
